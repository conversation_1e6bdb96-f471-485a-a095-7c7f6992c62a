## 改进版 VCBNet - 解决特征值范围不一致问题
import torch
import torch.nn as nn
import torch.nn.functional as F
from functools import partial
from timm.models.layers import DropPath, to_2tuple, trunc_normal_
from timm.models.registry import register_model
import math

class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features
        self.fc1 = nn.Conv2d(in_features, hidden_features, 1)
        self.dwconv = DWConv(hidden_features)
        self.act = act_layer()
        self.fc2 = nn.Conv2d(hidden_features, out_features, 1)
        self.drop = nn.Dropout(drop)	
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x):
        x = self.fc1(x)
        x = self.dwconv(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x

class LKA(nn.Module):
    def __init__(self, dim):
        super().__init__()
        self.conv0 = nn.Conv2d(dim, dim, 5, padding=2, groups=dim)
        self.conv_spatial = nn.Conv2d(dim, dim, 7, stride=1, padding=9, groups=dim, dilation=3)
        self.conv1 = nn.Conv2d(dim, dim, 1)

    def forward(self, x):
        u = x.clone()        
        attn = self.conv0(x)
        attn = self.conv_spatial(attn)
        attn = self.conv1(attn)
        return u * attn

class Attention(nn.Module):
    def __init__(self, d_model):
        super().__init__()
        self.proj_1 = nn.Conv2d(d_model, d_model, 1)
        self.activation = nn.GELU()
        self.spatial_gating_unit = LKA(d_model)
        self.proj_2 = nn.Conv2d(d_model, d_model, 1)

    def forward(self, x):
        shorcut = x.clone()
        x = self.proj_1(x)
        x = self.activation(x)
        x = self.spatial_gating_unit(x)
        x = self.proj_2(x)
        x = x + shorcut
        return x

class Block(nn.Module):
    def __init__(self, dim, mlp_ratio=4., drop=0.,drop_path=0., act_layer=nn.GELU):
        super().__init__()
        self.norm1 = nn.BatchNorm2d(dim)
        self.attn = Attention(dim)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()

        self.norm2 = nn.BatchNorm2d(dim)
        mlp_hidden_dim = int(dim * mlp_ratio)
        self.mlp = Mlp(in_features=dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop)
        layer_scale_init_value = 1e-2            
        self.layer_scale_1 = nn.Parameter(
            layer_scale_init_value * torch.ones((dim)), requires_grad=True)
        self.layer_scale_2 = nn.Parameter(
            layer_scale_init_value * torch.ones((dim)), requires_grad=True)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x):
        x = x + self.drop_path(self.layer_scale_1.unsqueeze(-1).unsqueeze(-1) * self.attn(self.norm1(x)))
        x = x + self.drop_path(self.layer_scale_2.unsqueeze(-1).unsqueeze(-1) * self.mlp(self.norm2(x)))
        return x

class OverlapPatchEmbed(nn.Module):
    """ Image to Patch Embedding """
    def __init__(self, img_size=224, patch_size=7, stride=4, in_chans=3, embed_dim=768):
        super().__init__()
        patch_size = to_2tuple(patch_size)
        self.proj = nn.Conv2d(in_chans, embed_dim, kernel_size=patch_size, stride=stride,
                              padding=(patch_size[0] // 2, patch_size[1] // 2))
        self.norm = nn.BatchNorm2d(embed_dim)
        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward(self, x):
        x = self.proj(x)
        _, _, H, W = x.shape
        x = self.norm(x)        
        return x, H, W

class VAN(nn.Module):
    def __init__(self, img_size=224, in_chans=3, num_classes=1000, embed_dims=[64, 128, 256, 512],
                mlp_ratios=[4, 4, 4, 4], drop_rate=0., drop_path_rate=0.3, norm_layer=nn.LayerNorm,
                 depths=[3, 4, 6, 3], num_stages=4, flag=False):
        super().__init__()
        if flag == False:
            self.num_classes = num_classes
        self.depths = depths
        self.num_stages = num_stages

        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]
        cur = 0
        
        for i in range(num_stages):
            patch_embed = OverlapPatchEmbed(img_size=img_size if i == 0 else img_size // (2 ** (i + 1)),
                                            patch_size=7 if i == 0 else 3,
                                            stride=2,
                                            in_chans=in_chans if i == 0 else embed_dims[i - 1],
                                            embed_dim=embed_dims[i])

            block = nn.ModuleList([Block(
                dim=embed_dims[i], mlp_ratio=mlp_ratios[i], drop=drop_rate, drop_path=dpr[cur + j])
                for j in range(depths[i])])
            norm = norm_layer(embed_dims[i])
            cur += depths[i]

            setattr(self, f"patch_embed{i + 1}", patch_embed)
            setattr(self, f"block{i + 1}", block)
            setattr(self, f"norm{i + 1}", norm)

        self.apply(self._init_weights)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def forward_features(self, x):
        B = x.shape[0]
        outs = []
        for i in range(self.num_stages):
            patch_embed = getattr(self, f"patch_embed{i + 1}")
            block = getattr(self, f"block{i + 1}")
            norm = getattr(self, f"norm{i + 1}")
            x, H, W = patch_embed(x)
            for blk in block:
                x = blk(x)
            x = x.flatten(2).transpose(1, 2)  
            x = norm(x)
            x = x.reshape(B, H, W, -1).permute(0, 3, 1, 2).contiguous()
            outs.append(x)
        return tuple(outs)

    def forward(self, x):
        x = self.forward_features(x)
        return x

class DWConv(nn.Module):
    def __init__(self, dim=768):
        super(DWConv, self).__init__()
        self.dwconv = nn.Conv2d(dim, dim, 3, 1, 1, bias=True, groups=dim)

    def forward(self, x):
        x = self.dwconv(x)
        return x

model_urls = {
    "van_b0": "https://huggingface.co/Visual-Attention-Network/VAN-Tiny-original/resolve/main/van_tiny_754.pth.tar",
    "van_b1": "https://huggingface.co/Visual-Attention-Network/VAN-Small-original/resolve/main/van_small_811.pth.tar",
    "van_b2": "https://huggingface.co/Visual-Attention-Network/VAN-Base-original/resolve/main/van_base_828.pth.tar",
    "van_b3": "https://huggingface.co/Visual-Attention-Network/VAN-Large-original/resolve/main/van_large_839.pth.tar",
}

@register_model
def van_base(pretrained=False, **kwargs):
    model = VAN(
        embed_dims=[64, 128, 320, 512], mlp_ratios=[8, 8, 4, 4],
        norm_layer=partial(nn.LayerNorm, eps=1e-6), depths=[2, 2, 4, 2],
        **kwargs)
    if pretrained:
        url = model_urls['van_b1']
        checkpoint = torch.hub.load_state_dict_from_url(url=url, map_location="cpu", check_hash=True)
        model_dict = model.state_dict()
        pretrained_dict = {k: v for k, v in checkpoint["state_dict"].items() if k in model_dict}
        model_dict.update(pretrained_dict)
        print("update")
        model.load_state_dict(model_dict)
    return model

# ==================== 改进的U-Net组件 ====================
class UnifiedDoubleConv(nn.Module):
    """统一激活函数的双卷积层 - 使用GELU保持与VAN一致"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.double_conv = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.GELU(),  # 统一使用GELU
            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.GELU()   # 统一使用GELU
        )

    def forward(self, x):
        return self.double_conv(x)

class UnifiedDown(nn.Module):
    """统一激活函数的下采样层"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.maxpool_conv = nn.Sequential(
            nn.MaxPool2d(2),
            UnifiedDoubleConv(in_channels, out_channels)
        )

    def forward(self, x):
        return self.maxpool_conv(x)

class UnifiedUp(nn.Module):
    """统一激活函数的上采样层"""
    def __init__(self, in_channels, out_channels, bilinear=True):
        super().__init__()
        if bilinear:
            self.up = nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True)
        else:
            self.up = nn.ConvTranspose2d(in_channels, in_channels // 2, kernel_size=2, stride=2)
        self.conv = UnifiedDoubleConv(in_channels, out_channels)

    def forward(self, x1, x2):
        x1 = self.up(x1)
        x = torch.cat([x2, x1], dim=1)
        return self.conv(x)

# ==================== 特征融合模块 ====================
class FeatureNormalizer(nn.Module):
    """特征标准化模块 - 解决特征值范围不一致问题"""
    def __init__(self, channels, eps=1e-5):
        super().__init__()
        self.eps = eps
        self.gamma = nn.Parameter(torch.ones(1, channels, 1, 1))
        self.beta = nn.Parameter(torch.zeros(1, channels, 1, 1))

    def forward(self, x):
        # 计算空间维度的均值和标准差
        mean = x.mean(dim=[2, 3], keepdim=True)
        std = x.std(dim=[2, 3], keepdim=True)
        # 标准化
        normalized = (x - mean) / (std + self.eps)
        # 可学习的缩放和偏移
        return self.gamma * normalized + self.beta

class SpatialAttention(nn.Module):
    """空间注意力模块"""
    def __init__(self, channels):
        super().__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(channels * 2, channels, 3, padding=1),
            nn.BatchNorm2d(channels),
            nn.GELU(),
            nn.Conv2d(channels, 1, 1),
            nn.Sigmoid()
        )

    def forward(self, feat1, feat2):
        combined = torch.cat([feat1, feat2], dim=1)
        attention_map = self.conv(combined)
        return attention_map

class ChannelAttention(nn.Module):
    """通道注意力模块"""
    def __init__(self, channels, reduction=16):
        super().__init__()
        self.avg_pool = nn.AdaptiveAvgPool2d(1)
        self.max_pool = nn.AdaptiveMaxPool2d(1)

        self.fc = nn.Sequential(
            nn.Conv2d(channels, channels // reduction, 1, bias=False),
            nn.GELU(),
            nn.Conv2d(channels // reduction, channels, 1, bias=False)
        )
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        avg_out = self.fc(self.avg_pool(x))
        max_out = self.fc(self.max_pool(x))
        out = avg_out + max_out
        return self.sigmoid(out)

class AdaptiveFusion(nn.Module):
    """自适应特征融合模块 - 核心改进"""
    def __init__(self, channels):
        super().__init__()
        # 特征标准化
        self.van_norm = FeatureNormalizer(channels)
        self.unet_norm = FeatureNormalizer(channels)

        # 注意力机制
        self.spatial_attention = SpatialAttention(channels)
        self.channel_attention = ChannelAttention(channels)

        # 融合权重网络
        self.weight_net = nn.Sequential(
            nn.Conv2d(channels * 2, channels // 4, 1),
            nn.GELU(),
            nn.Conv2d(channels // 4, 2, 1),
            nn.Softmax(dim=1)
        )

        # 最终融合卷积
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1),
            nn.BatchNorm2d(channels),
            nn.GELU()
        )

    def forward(self, van_feat, unet_feat):
        # 1. 特征标准化 - 解决值范围不一致问题
        van_norm = self.van_norm(van_feat)
        unet_norm = self.unet_norm(unet_feat)

        # 2. 空间注意力融合
        spatial_att = self.spatial_attention(van_norm, unet_norm)
        van_spatial = van_norm * spatial_att
        unet_spatial = unet_norm * (1 - spatial_att)

        # 3. 通道注意力
        van_channel_att = self.channel_attention(van_spatial)
        unet_channel_att = self.channel_attention(unet_spatial)

        van_refined = van_spatial * van_channel_att
        unet_refined = unet_spatial * unet_channel_att

        # 4. 自适应权重融合
        combined = torch.cat([van_refined, unet_refined], dim=1)
        weights = self.weight_net(combined)

        fused = van_refined * weights[:, 0:1] + unet_refined * weights[:, 1:2]

        # 5. 最终融合卷积
        output = self.fusion_conv(fused)

        return output

class RegressionHead(nn.Module):
    """回归输出头"""
    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.head = nn.Sequential(
            nn.Conv2d(in_channels, in_channels // 2, kernel_size=3, padding=1),
            nn.BatchNorm2d(in_channels // 2),
            nn.GELU(),
            nn.Dropout2d(0.1),
            nn.Conv2d(in_channels // 2, out_channels, kernel_size=1)
        )

    def forward(self, x):
        return self.head(x)

# ==================== 改进版VCBNet ====================
class VCBNetImproved(nn.Module):
    """
    改进版VCBNet - 解决特征值范围不一致和融合问题

    主要改进:
    1. 统一激活函数 (GELU)
    2. 特征标准化
    3. 自适应融合机制
    4. 注意力机制
    """
    def __init__(self, n_channels=3, n_outputs=1, bilinear=False, pretrained=True):
        super(VCBNetImproved, self).__init__()
        self.n_channels = n_channels
        self.n_outputs = n_outputs
        self.bilinear = bilinear

        # VAN骨干网络
        self.backbone = van_base(pretrained=pretrained)

        # U-Net分支 - 使用统一的激活函数
        self.conv = UnifiedDoubleConv(n_channels, 32)
        self.down1_1 = UnifiedDown(32, 64)
        self.down1_2 = UnifiedDown(64, 128)
        self.down1_3 = UnifiedDown(128, 256)
        self.down1_4 = UnifiedDown(256, 512)

        self.up1_1 = UnifiedUp(512, 256, bilinear)
        self.up1_2 = UnifiedUp(256, 128, bilinear)
        self.up1_3 = UnifiedUp(128, 64, bilinear)
        self.up1_4 = UnifiedUp(64, 32, bilinear)

        # VAN分支的解码器 - 统一激活函数
        self.van_up4 = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(512, 256, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(256),
            nn.GELU()
        )
        self.van_up4_conv = nn.Conv2d(576, 256, 1)  # 256 + 320 = 576

        self.van_up3 = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(256, 128, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(128),
            nn.GELU()
        )
        self.van_up3_conv = nn.Conv2d(256, 128, 1)  # 128 + 128 = 256

        self.van_up2 = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(128, 64, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(64),
            nn.GELU()
        )
        self.van_up2_conv = nn.Conv2d(128, 64, 1)  # 64 + 64 = 128

        self.van_up1 = nn.Sequential(
            nn.Upsample(scale_factor=2, mode='bilinear', align_corners=True),
            nn.Conv2d(64, 32, kernel_size=3, stride=1, padding=1),
            nn.BatchNorm2d(32),
            nn.GELU()
        )

        # 自适应融合模块
        self.adaptive_fusion = AdaptiveFusion(32)

        # 回归输出头
        self.regression_head = RegressionHead(32, n_outputs)

        # 可选: 分支独立输出 (用于对比实验)
        self.van_head = RegressionHead(32, n_outputs)
        self.unet_head = RegressionHead(32, n_outputs)

    def forward(self, x, return_individual=False):
        """
        前向传播

        Args:
            x: 输入图像 [B, C, H, W]
            return_individual: 是否返回各分支的独立输出

        Returns:
            如果return_individual=False: 融合后的输出
            如果return_individual=True: (融合输出, VAN输出, U-Net输出)
        """
        # VAN分支
        van_features = self.backbone(x)
        stage1, stage2, stage3, stage4 = van_features

        # VAN解码器
        van_up4 = self.van_up4(stage4)
        van_up4 = torch.cat([van_up4, stage3], dim=1)
        van_up4 = self.van_up4_conv(van_up4)

        van_up3 = self.van_up3(van_up4)
        van_up3 = torch.cat([van_up3, stage2], dim=1)
        van_up3 = self.van_up3_conv(van_up3)

        van_up2 = self.van_up2(van_up3)
        van_up2 = torch.cat([van_up2, stage1], dim=1)
        van_up2 = self.van_up2_conv(van_up2)

        van_feat = self.van_up1(van_up2)  # [B, 32, H, W]

        # U-Net分支
        x1_1 = self.conv(x)
        x1_2 = self.down1_1(x1_1)
        x1_3 = self.down1_2(x1_2)
        x1_4 = self.down1_3(x1_3)
        x1_5 = self.down1_4(x1_4)

        x1_6 = self.up1_1(x1_5, x1_4)
        x1_7 = self.up1_2(x1_6, x1_3)
        x1_8 = self.up1_3(x1_7, x1_2)
        unet_feat = self.up1_4(x1_8, x1_1)  # [B, 32, H, W]

        # 自适应融合
        fused_feat = self.adaptive_fusion(van_feat, unet_feat)

        # 输出
        fused_output = self.regression_head(fused_feat)

        if return_individual:
            van_output = self.van_head(van_feat)
            unet_output = self.unet_head(unet_feat)
            return fused_output, van_output, unet_output
        else:
            return fused_output

    def get_feature_stats(self, x):
        """获取特征统计信息 - 用于调试"""
        with torch.no_grad():
            # 获取特征
            van_features = self.backbone(x)
            stage1, stage2, stage3, stage4 = van_features

            # VAN解码到32通道
            van_up4 = self.van_up4(stage4)
            van_up4 = torch.cat([van_up4, stage3], dim=1)
            van_up4 = self.van_up4_conv(van_up4)
            van_up3 = self.van_up3(van_up4)
            van_up3 = torch.cat([van_up3, stage2], dim=1)
            van_up3 = self.van_up3_conv(van_up3)
            van_up2 = self.van_up2(van_up3)
            van_up2 = torch.cat([van_up2, stage1], dim=1)
            van_up2 = self.van_up2_conv(van_up2)
            van_feat = self.van_up1(van_up2)

            # U-Net编码解码到32通道
            x1_1 = self.conv(x)
            x1_2 = self.down1_1(x1_1)
            x1_3 = self.down1_2(x1_2)
            x1_4 = self.down1_3(x1_3)
            x1_5 = self.down1_4(x1_4)
            x1_6 = self.up1_1(x1_5, x1_4)
            x1_7 = self.up1_2(x1_6, x1_3)
            x1_8 = self.up1_3(x1_7, x1_2)
            unet_feat = self.up1_4(x1_8, x1_1)

            # 计算统计信息
            van_stats = {
                'mean': van_feat.mean().item(),
                'std': van_feat.std().item(),
                'min': van_feat.min().item(),
                'max': van_feat.max().item()
            }

            unet_stats = {
                'mean': unet_feat.mean().item(),
                'std': unet_feat.std().item(),
                'min': unet_feat.min().item(),
                'max': unet_feat.max().item()
            }

            return van_stats, unet_stats

if __name__ == '__main__':
    # 测试改进版VCBNet
    model = VCBNetImproved(n_channels=3, n_outputs=1, pretrained=False)
    model.eval()

    # 创建测试输入
    test_input = torch.randn(1, 3, 512, 512)
    print(f"输入张量形状: {test_input.shape}")

    # 前向传播测试
    with torch.no_grad():
        # 测试融合输出
        fused_out = model(test_input)
        print(f"融合输出形状: {fused_out.shape}")

        # 测试独立输出
        fused_out, van_out, unet_out = model(test_input, return_individual=True)
        print(f"VAN输出形状: {van_out.shape}")
        print(f"U-Net输出形状: {unet_out.shape}")

        # 获取特征统计信息
        van_stats, unet_stats = model.get_feature_stats(test_input)
        print(f"\nVAN特征统计: {van_stats}")
        print(f"U-Net特征统计: {unet_stats}")

    # 计算模型参数量
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"\n总参数量: {total_params:,}")
    print(f"可训练参数量: {trainable_params:,}")
