import os
os.environ["CUDA_VISIBLE_DEVICES"] = "0"
import os.path as osp
import json
import torch
# import pickle #不再需要，因为 accelerator.save/load 处理字典
import logging
import numpy as np
from tqdm import tqdm
from accelerate import Accelerator, DistributedDataParallelKwargs
from accelerate.utils import TorchDynamoPlugin
from accelerate.utils import set_seed, ProjectConfiguration
from model import *
#from model.s-vit import SVit
from utils import *
#from API import dataloader.load_data
from API import *
from loss import *
from opt import *

from diffusers import (
    get_constant_schedule_with_warmup,
    get_linear_schedule_with_warmup,
    get_cosine_schedule_with_warmup,
)
torch.backends.cudnn.benchmark = True  ## 寻找合适的卷积方式 优化 ！

# 脚本开始时尝试清空CUDA缓存，可能有助于在某些情况下减少显存碎片
#torch.cuda.empty_cache # 保持原样
class Exp:
    def __init__(self, args):
        super(Exp, self).__init__()
        self.args = args
        self.config = self.args.__dict__
        set_seed(self.args.seed)
        self.path = osp.join(self.args.res_dir, self.args.ex_name)
        check_dir(self.path)
        self.eval_step = self.args.eval_step
        self.checkpoints_path = osp.join(self.path, 'checkpoints')
        check_dir(self.checkpoints_path)

        self.log_path = osp.join(self.path, 'logs')
        check_dir(self.log_path)

        self.best_val_loss = float('inf')
        self.early_stopping_counter = 0
        self.early_stopping_patience = getattr(args, 'early_stopping_patience', 10)

        ddp_plugin = DistributedDataParallelKwargs(find_unused_parameters=True,broadcast_buffers=False)  ##ＢＣＵＮｅｔ　要设置Ｔｒｕｅ！
        project_config = ProjectConfiguration(project_dir=self.args.res_dir,
                                              logging_dir=self.log_path)
        dynamo_plugin = TorchDynamoPlugin(
            backend="inductor",  # Options: "inductor", "aot_eager", "aot_nvfuser", etc.
            mode="default",      # Options: "default", "reduce-overhead", "max-autotune"
         )

        self.accelerator = Accelerator(
            split_batches=True,
            mixed_precision=self.args.mixed_precision if hasattr(args, 'mixed_precision') else 'no',
            project_config=project_config,
            gradient_accumulation_steps=self.args.gradient_accumulation_steps,
            kwargs_handlers=[ddp_plugin],
            dynamo_plugin=dynamo_plugin
        )
        self.device = self.accelerator.device
        self._preparation()
        if self.accelerator.is_main_process:
            print_log(output_namespace(self.args))
            print_log(f"Using mixed precision: {self.args.mixed_precision if hasattr(args, 'mixed_precision') else 'no'}")
            print_log(f"Number of processes: {self.accelerator.num_processes}")
            print_log(f"Distributed type: {self.accelerator.distributed_type}")

        self._get_data()
        self._build_model()
        self._select_optimizer()
        self._select_criterion()
        ## ------------------- 增加 complie----------------
        #if hasattr(torch, "compile"):
        #    if self.accelerator.is_main_process:
        #        # 使用你自己的日志函数 print_log
        #        print_log("PyTorch 2.0+ detected. Compiling the model... This may take a moment.")
        #    
        #    对模型进行编译，使用默认模式
        #    默认模式在性能和编译时间之间取得了很好的平衡
        #    self.model = torch.compile(self.model)
        #else:
        #    if self.accelerator.is_main_process:
        #        print_log("PyTorch version < 2.0. Skipping model compilation.")
        ##--------------------------结束------------------------
        self.model, self.optimizer, self.scheduler, self.train_loader, self.vali_loader, self.test_loader = self.accelerator.prepare(
            self.model, self.optimizer, self.scheduler, self.train_loader, self.vali_loader, self.test_loader
        )

    def _preparation(self):
        sv_param = osp.join(self.path, 'model_param.json')
        if self.accelerator.is_main_process:
            with open(sv_param, 'w') as file_obj:
                json.dump(self.args.__dict__, file_obj)

            for handler in logging.root.handlers[:]:
                logging.root.removeHandler(handler)
            logging.basicConfig(level=logging.INFO, filename=osp.join(self.log_path, 'log.log'),
                                filemode='a', format='%(asctime)s - %(message)s')

    def _build_model(self):
        args = self.args
        if args.model_type == 'unet':
            self.model = UNet(in_chans = args.in_shape[0],
                              out_chans=args.out_chans,
                              channels=args.unet_channels,
                              skip=args.unet_skip)
            if self.accelerator.is_main_process:
                print_log(f"使用UNet模型，通道数: {args.unet_channels}")
        elif args.model_type == 'vit':
            # 将列表转换为元组，以便pair函数可以正确处理
            image_size = tuple(args.in_shape[1:])
            # 确保patch_size是元组格式(patch_size, patch_size)
            patch_size = (args.vit_patch_size, args.vit_patch_size)
            self.model = ViT(image_size=image_size,
                             patch_size=patch_size,
                             in_chans=args.in_shape[0],
                             out_chans=args.out_chans,
                             dim=args.vit_dim,
                             depth=args.vit_depth,
                             heads=args.vit_heads,
                             mlp_dim=args.vit_mlp_dim,
                             dim_head=args.vit_dim_head)
            if self.accelerator.is_main_process:
                print_log(f"使用ViT模型，特征维度: {args.vit_dim}, 深度: {args.vit_depth}, 头数: {args.vit_heads}")
        elif args.model_type == 's-vit':
            image_size = tuple(args.in_shape[1:])
            patch_size = (args.vit_patch_size, args.vit_patch_size)
            self.model = SVit(image_size=image_size,
                              patch_size=patch_size,
                              in_chans=args.in_shape[0],
                              out_chans=args.out_chans,
                              dim=args.vit_dim,
                              depth=args.vit_depth,
                              heads=args.vit_heads,
                              mlp_dim=args.vit_mlp_dim,
                              dim_head=args.vit_dim_head,
                              freeze_vit=args.freeze_vit,
                              hidden_layers=args.hidden_layers,
                              load_pretrained=args.load_pretrained,
                              pretrained_path=args.pretrained_path)
            if self.accelerator.is_main_process:
                print_log(f"使用S-ViT模型，特征维度: {args.vit_dim}, 深度: {args.vit_depth}, 头数: {args.vit_heads}")
        elif args.model_type == 'bcunet':   ## 增加的bcunet
            assert args.in_shape[0] == 3, "BCUNet model requires input channels to be 3."
            self.model = BCUNet(
                n_channels=args.in_shape[0],  # 用户确认BCUNet的输入通道数为3，为了使用预训练参数，固定为3
                n_outputs=args.out_chans
            )
            if self.accelerator.is_main_process:
                print_log(f"使用BCUNet模型，输入通道数: 3, 输出通道数: {args.out_chans}")
        elif args.model_type == 'vcbnet':  ##增加的vcbnet
            assert args.in_shape[0] == 3, "VCBNet model requires input channels to be 3."
            self.model = VCBNet(
                n_channels=args.in_shape[0],  
                n_outputs=args.out_chans
            )
            if self.accelerator.is_main_process:
                print_log(f"使用VCBNet模型，输入通道数: 3, 输出通道数: {args.out_chans}")
        elif args.model_type == 'vcbnet1':  ##增加的vcbnet
            assert args.in_shape[0] == 3, "VCBNet model requires input channels to be 3."
            self.model = VCBNet1(
                n_channels=args.in_shape[0],  
                n_outputs=args.out_chans
            )
            if self.accelerator.is_main_process:
                print_log(f"使用VCBNet1模型，输入通道数: 3, 输出通道数: {args.out_chans}")
        elif args.model_type == 'vcbnet2':  ##增加的vcbnet2
            assert args.in_shape[0] == 3, "VCBNet2 model requires input channels to be 3."
            self.model = VCBNet2(
                n_channels=args.in_shape[0],  
                n_outputs=args.out_chans
            )
            if self.accelerator.is_main_process:
                print_log(f"使用VCBNet2模型，输入通道数: 3, 输出通道数: {args.out_chans}")
        elif args.model_type == 'vunet':
            assert args.in_shape[0] == 3, "VUNet model requires input channels to be 3."
            self.model = VUNet(
                n_channels=args.in_shape[0],
                n_outputs=args.out_chans
            )
        elif args.model_type == 'vcbnet_fusion':
            assert args.in_shape[0] == 3, "VUNetFusion model requires input channels to be 3."
            self.model = VUNetFusion(
                n_channels=args.in_shape[0],
                n_outputs=args.out_chans
            )   
        elif args.model_type == 'vcbfnet':
            assert args.in_shape[0] == 3, "VCBFNet model requires input channels to be 3."
            self.model = VCBFNet(
                n_channels=args.in_shape[0],
                n_outputs=args.out_chans
            )
        elif args.model_type == 'vcbnet2':
            assert args.in_shape[0] == 3, "VCBFNet model requires input channels to be 3."
            self.model = VCBNet2(
                n_channels=args.in_shape[0],
                n_outputs=args.out_chans
            )
        else:
            raise ValueError(f"不支持的模型类型: {args.model_type}")
        
        if self.accelerator.is_main_process:
            print_log(f"模型参数数量: {sum(p.numel() for p in self.model.parameters() if p.requires_grad)}")


    def _get_data(self):
        config = self.args.__dict__
        self.train_loader, self.vali_loader, self.test_loader, self.data_max, self.data_min = load_data(**config)
        self.vali_loader = self.test_loader if self.vali_loader is None else self.vali_loader
        self.test_loader = self.vali_loader if self.test_loader is None else self.test_loader

    def _select_optimizer(self):
        if self.args.optimizer == 'adam':
            self.optimizer = torch.optim.Adam(self.model.parameters(), lr=self.args.lr)
        elif self.args.optimizer == 'adamw':
            self.optimizer = torch.optim.AdamW(self.model.parameters(), lr=self.args.lr)
        elif self.args.optimizer == 'sgd':
            self.optimizer = torch.optim.SGD(self.model.parameters(), lr=self.args.lr)
        elif self.args.optimizer == 'lion':
            self.optimizer = Lion(self.model.parameters(), lr=self.args.lr)
        else:
              raise ValueError("Invalid optimizer_type.")      

        num_steps_per_epoch = len(self.train_loader)
        total_steps = (num_steps_per_epoch // self.args.gradient_accumulation_steps) * self.args.epochs
        warmup_steps = self.args.warmup_steps

        if self.args.scheduler == 'constant':
            self.scheduler = get_constant_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
            )
        elif self.args.scheduler == 'linear':
            self.scheduler = get_linear_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=total_steps,
            )
        elif self.args.scheduler == 'cosine':
            self.scheduler = get_cosine_schedule_with_warmup(
                self.optimizer,
                num_warmup_steps=warmup_steps,
                num_training_steps=total_steps,
            )
        else:
            raise ValueError(f"Invalid scheduler_type. Expected 'constant', 'linear', or 'cosine', got: {self.args.scheduler}")

    def _select_criterion(self):
        # 根据 args.loss_type 选择损失函数
        if self.args.loss_type == 'mse':
            self.criterion = MSELoss()
        elif self.args.loss_type == 'mae':
            self.criterion = L1Loss() # L1Loss 对应 'mae'
        elif self.args.loss_type == 'wmse':
            self.criterion = WMSELoss()
        elif self.args.loss_type == 'wmae':
            self.criterion = WMAELoss()
        elif self.args.loss_type == 'hmse':
            self.criterion = HMSELoss()
        elif self.args.loss_type == 'hmae':
            self.criterion = HMAELoss()
        elif self.args.loss_type == 'csi':
            self.criterion = CSILoss(
                thresholds=self.args.metrics_thresholds,
                data_max=self.data_max,
                data_min=self.data_min,
                alpha=self.args.loss_alpha
            )
        elif self.args.loss_type == 'pod':
            self.criterion = PODLoss(
                thresholds=self.args.metrics_thresholds,
                data_max=self.data_max,
                data_min=self.data_min,
                alpha=self.args.loss_alpha
            )
        elif self.args.loss_type == '3wmse':
            self.criterion = MSE3Loss()
        else:
            raise ValueError(f"Unsupported loss type: {self.args.loss_type}. Supported types are 'mse', 'mae', 'wmse', 'wmae'.")
        
        if self.accelerator.is_main_process: # 可选：增加日志打印当前使用的损失函数
            print_log(f"Using loss function: {self.args.loss_type}")

    def _save(self, name=''):
        if not self.accelerator.is_main_process:
            return
        unwrapped_model = self.accelerator.unwrap_model(self.model)
        checkpoint = {
            'model': unwrapped_model.state_dict(), # 保存 unwrapped 模型权重，确保可移植性
            'optimizer': self.optimizer.state_dict(),
            'scheduler': self.scheduler.state_dict(),
        }
        self.accelerator.save(checkpoint, osp.join(self.checkpoints_path, name + '.pth'))
        if self.accelerator.is_main_process: # 添加日志
            print_log(f"检查点已保存到: {osp.join(self.checkpoints_path, name + '.pth')}")

    def _load(self, path):
        if not os.path.exists(path):
            if self.accelerator.is_main_process:
                print_log(f"Warning: 模型文件 {path} 不存在，跳过加载")
            return False

        try:
            # Accelerator 的 load 方法不直接用于加载这种自定义字典，所以仍用 torch.load
            # 但因为保存的是 unwrapped_model.state_dict()，加载逻辑也需要对应
            if self.accelerator.is_main_process: print_log(f"尝试从 {path} 加载检查点...")
            checkpoint = torch.load(path, map_location=self.device)

            unwrapped_model = self.accelerator.unwrap_model(self.model)
            unwrapped_model.load_state_dict(checkpoint['model'])
            #self.model = self.accelerator.prepare(unwrapped_model) # 不需要再次 prepare 模型本身，因为权重已更新

            self.optimizer.load_state_dict(checkpoint['optimizer'])
            self.scheduler.load_state_dict(checkpoint['scheduler'])

            if self.accelerator.is_main_process:
                print_log(f"从 {path} 加载检查点成功。")
            return True
        except Exception as e:
            if self.accelerator.is_main_process:
                print_log(f"加载检查点 {path} 失败: {e}。")
            return False

    def train(self, args):
        config = args.__dict__

        self.model.train()  # 设置模型为训练模式
        accumulation_steps = self.args.gradient_accumulation_steps  # 直接使用 self.args 中的值
        for epoch in range(config['epochs']):
            train_loss = []
            train_pbar = tqdm(self.train_loader, disable=not self.accelerator.is_main_process)
            self.optimizer.zero_grad()  # 在 epoch 开始时清空梯度

            for batch_idx, (batch_x, batch_y) in enumerate(train_pbar):
                with self.accelerator.autocast(): # 明确使用 autocast 进行混合精度
                    pred_y = self.model(batch_x)
                    loss = self.criterion(pred_y, batch_y)
                
                # 梯度累积：将损失除以累积步数
                loss = loss / accumulation_steps
                self.accelerator.backward(loss)
  
                # 检查是否达到累积步数或最后一个 batch
                if (batch_idx + 1) % accumulation_steps == 0 or (batch_idx + 1) == len(self.train_loader):
                    self.accelerator.wait_for_everyone()
                    if self.accelerator.sync_gradients:
                        clip_value = self.args.gradient_clipping if hasattr(self.args, 'gradient_clipping') else 1.0
                        if clip_value > 0:
                            self.accelerator.clip_grad_norm_(self.model.parameters(), clip_value)

                    self.optimizer.step()
                    if not self.accelerator.optimizer_step_was_skipped:
                        self.scheduler.step()
                    self.optimizer.zero_grad()  # 累积完成后清空梯度
                train_loss.append(loss.item() * accumulation_steps)
                current_lr = self.optimizer.param_groups[0]['lr']
                train_pbar.set_description('train loss: {:.4f}, lr: {:.6f}'.format(loss.item(), current_lr))

            local_train_loss = np.average(train_loss) if train_loss else 0.0
            gathered_train_loss = self.accelerator.gather(torch.tensor(local_train_loss, device=self.device)).mean().item()

            if epoch % args.log_step == 0:
                with torch.no_grad(): # 验证时不需要计算梯度
                    vali_loss = self.vali(self.vali_loader, epoch + 1)
                    if self.accelerator.is_main_process:
                        if epoch > 0 and epoch % (args.log_step * 100) == 0:
                            self._save(name=str(epoch))
                        current_lr = self.optimizer.param_groups[0]['lr']
                        print_log("Epoch: {0} | Train Loss: {1:.6f} Vali Loss: {2:.6f} LR: {3:.6f}\n".format(
                            epoch + 1, gathered_train_loss, vali_loss, current_lr))
                        # 检查当前验证损失是否优于已记录的最佳损失
                        if  vali_loss < self.best_val_loss:
                            self.best_val_loss = vali_loss # 更新最佳损失
                            print_log(f"New best validation loss: {self.best_val_loss:.6f}. Saving checkpoint...")
                            self._save(name='checkpoint')
                            self.early_stopping_counter = 0
                        else:
                            self.early_stopping_counter += 1
                            if self.early_stopping_counter >= self.early_stopping_patience:
                                print_log("Early stopping triggered.")  
                                # 新增
                                self.accelerator.set_trigger()
                                #self.accelerator.wait_for_everyone()
                                #break
            self.accelerator.wait_for_everyone()   #新增 开始
            ##  保存 每一个epoch
            self._save(name=f'checkpoint_{epoch}')
            if self.accelerator.check_trigger():
                if self.accelerator.is_main_process: 
                    print_log(f"所有进程均触发早停。停止训练。")
                break                               #新增 结束
        self.accelerator.wait_for_everyone()
        best_model_path = osp.join(self.checkpoints_path, 'checkpoint.pth')
        if self.accelerator.is_main_process:
            print_log(f"加载最佳模型: {best_model_path}")

        if osp.exists(best_model_path):
            self._load(best_model_path)
        else:
            if self.accelerator.is_main_process:
                print_log(f"Warning: 最佳模型文件 {best_model_path} 未找到。将使用最后一个epoch的模型进行测试。")

        self.accelerator.wait_for_everyone()

    @torch.no_grad()
    def test(self, args): 
        self.model.eval()
        test_pbar = tqdm(self.test_loader, disable=not self.accelerator.is_main_process)
        
        # all_batch_test_losses = [] # 您的 test 片段没有这个
        
        accumulated_weighted_sums = {} # 用于累积所有指标的加权和
        num_samples = 0 # 与您的变量名一致

        for batch_x, batch_y in test_pbar: 
            with self.accelerator.autocast():
                pred_y = self.model(batch_x) 
            
            gathered_pred = self.accelerator.gather(pred_y)
            gathered_batch_y = self.accelerator.gather(batch_y)

            if self.accelerator.is_main_process:
                pred_np = gathered_pred.detach().cpu().numpy()
                batch_y_np = gathered_batch_y.detach().cpu().numpy()

                # --- 修改开始: 调用 metric 并累积所有指标 ---
                current_batch_metrics_dict = metric(pred_np, batch_y_np,
                                                   self.data_max, 
                                                   self.data_min,
                                                   self.args.metrics_thresholds) 

                actual_batch_size = pred_np.shape[0] # 保持您的变量名
                
                for key, value in current_batch_metrics_dict.items():
                    if key not in accumulated_weighted_sums:
                        accumulated_weighted_sums[key] = 0.0
                    accumulated_weighted_sums[key] += value * actual_batch_size
                
                num_samples += actual_batch_size
        
        mse_avg_to_return = 0.0 # 保持您的返回值变量名
        if self.accelerator.is_main_process and num_samples > 0:
            # --- 修改开始: 使用 print_log 打印所有平均指标 ---
            print_log('Test metrics:') # 与您的原始日志头一致
            final_avg_metrics = {}

            for key, total_sum in accumulated_weighted_sums.items():
                avg_value = total_sum / num_samples
                final_avg_metrics[key] = avg_value
                # 逐个打印所有新指标
                print_log(f'  {key}: {avg_value:.6f}')  # Test的打印格式通常前面有空格
            
            if 'mse_denorm' in final_avg_metrics:
                mse_avg_to_return = final_avg_metrics['mse_denorm']
            
            # 您的原始 print_log 格式，用于 mse 和 mae
            #mse_for_original_log = final_avg_metrics.get('mse_denorm', 0.0)
            #mae_for_original_log = final_avg_metrics.get('mae_denorm', 0.0)
            #print_log('Original format test mse:{:.4f}, mae:{:.4f}'.format(
            #    mse_for_original_log, mae_for_original_log))
            # --- 修改结束 ---
        self.accelerator.wait_for_everyone() 
        return mse_avg_to_return

    @torch.no_grad()
    def vali(self, vali_loader, current_epoch_num): 
        self.model.eval()
        total_loss = [] 
        
        accumulated_weighted_sums = {} 
        num_samples = 0 
        flag = self.eval_step
        should_calculate_and_print_detailed_metrics = (current_epoch_num % flag == 0)
        
        vali_pbar_desc = f"Epoch {current_epoch_num} Validation"
        if should_calculate_and_print_detailed_metrics and self.accelerator.is_main_process:
            vali_pbar_desc += " (Detailed Metrics)"
        
        vali_pbar = tqdm(vali_loader, disable=not self.accelerator.is_main_process, desc=vali_pbar_desc)

        for i, (batch_x, batch_y) in enumerate(vali_pbar): 
            # if i * batch_x.shape[0] > 1000 and self.accelerator.is_main_process: 
            #         break 

            with self.accelerator.autocast():
                pred_y = self.model(batch_x) 
            loss = self.criterion(pred_y, batch_y) 
            total_loss.append(loss.item()) 
            
            if should_calculate_and_print_detailed_metrics: 
                gathered_pred = self.accelerator.gather(pred_y)
                gathered_batch_y = self.accelerator.gather(batch_y)

                if self.accelerator.is_main_process: 
                    pred_np = gathered_pred.detach().cpu().numpy()
                    batch_y_np = gathered_batch_y.detach().cpu().numpy()
                    current_global_batch_size = pred_np.shape[0]
                    if current_global_batch_size == 0: continue
                    
                    batch_metrics_dict = metric(pred_np, batch_y_np,
                                                self.data_max, self.data_min,
                                                self.args.metrics_thresholds) 
                    
                    for key, value in batch_metrics_dict.items():
                        if key not in accumulated_weighted_sums: 
                            accumulated_weighted_sums[key] = 0.0
                        accumulated_weighted_sums[key] += value * current_global_batch_size
                    num_samples += current_global_batch_size # 改动: 使用 num_samples
        
        local_total_loss_avg = np.average(total_loss) if total_loss else 0.0
        gathered_total_loss_tensor = torch.tensor(float(local_total_loss_avg), device=self.device, dtype=torch.float32)
        gathered_avg_criterion_loss = self.accelerator.gather(gathered_total_loss_tensor).mean().item()

        if self.accelerator.is_main_process:
            if should_calculate_and_print_detailed_metrics and num_samples > 0: # 改动: 使用 num_samples
                print_log(f"--- Detailed Validation Metrics for Epoch {current_epoch_num} ---")
                for key, total_sum in accumulated_weighted_sums.items():
                    avg_value = total_sum / num_samples # 改动: 使用 num_samples
                    # 直接使用 print_log 输出每个计算出的平均指标
                    print_log(f'  vali {key}: {avg_value:.4f}') 
                print_log(f"--- End Detailed Validation Metrics for Epoch {current_epoch_num} ---")
                # --- 改动结束 ---
                # ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        
        self.model.train()  
        return gathered_avg_criterion_loss
